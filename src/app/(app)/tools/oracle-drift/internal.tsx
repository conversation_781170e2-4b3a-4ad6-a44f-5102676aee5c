"use client";
import Link from "next/link";
import { useState, useMemo } from "react";
import { AppLogo } from "@/app/components/app-logo";
import { AppPageTitle } from "@/app/components/app-page-title";
import Footer from "@/app/components/Footer/Footer";
import { doubleDrift } from "./lib";
import { AppInput } from "@/app/components/app-input";
import {
  evaluateFormula,
  defaultFormula,
  formulaExamples,
} from "./formula-evaluator";

export default function OracleDriftPageInternal() {
  const [oracleOnePrice, setOracleOnePrice] = useState<any>("358189942348");
  const [oracleOneDeviationThresholdBPS, setOracleOneDeviationThresholdBPS] =
    useState<any>("25");
  const [oracleTwoPrice, setOracleTwoPrice] = useState<any>("99989574");
  const [oracleTwoDeviationThresholdBPS, setOracleTwoDeviationThresholdBPS] =
    useState<any>("50");
  const [customFormula, setCustomFormula] = useState<string>(defaultFormula);
  const [formulaError, setFormulaError] = useState<string>("");

  const validateAndCall = (
    min: number,
    max: number,
    value: number,
    fn: (input: any) => any
  ) => {
    if (value < min || value > max) {
      return false;
    }
    return fn(value);
  };

  const formulaFn = useMemo(() => {
    try {
      setFormulaError("");
      const testResult = evaluateFormula(customFormula, { coll: 1, debt: 1 });
      return (coll: number, debt: number) =>
        evaluateFormula(customFormula, { coll, debt });
    } catch (error) {
      setFormulaError(error.message);
      return null;
    }
  }, [customFormula]);

  const result = useMemo(() => {
    if (!formulaFn) {
      return { spot: 0, max: 0, min: 0 };
    }
    try {
      return doubleDrift(
        Number(oracleOnePrice),
        Number(oracleOneDeviationThresholdBPS),
        Number(oracleTwoPrice),
        Number(oracleTwoDeviationThresholdBPS),
        formulaFn
      );
    } catch (error) {
      setFormulaError(`Error calculating result: ${error.message}`);
      return { spot: 0, max: 0, min: 0 };
    }
  }, [
    oracleOnePrice,
    oracleOneDeviationThresholdBPS,
    oracleTwoPrice,
    oracleTwoDeviationThresholdBPS,
    formulaFn,
  ]);

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="gradient-dark-bg flex items-center justify-between bg-back-neutral-tertiary px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      <div className="min-h-[800px] p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">Oracle Drift</AppPageTitle>

          <p className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
            Given 2 Oracle prices and their Deviation Threshold
          </p>
          <p className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
            The tool will calculate the max and min possible values for the
            oracle, and the max delta between the two.
          </p>
          <p className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
            NOTE: You can alter the formula by typing in the textarea or
            clicking the buttons for presets
          </p>
        </div>

        <div className="mb-[20px]">
          <div className="mb-[10px]">
            <label className="mb-[5px] block text-[14px] font-medium text-fore-neutral-primary">
              Custom Formula (use 'coll' for Oracle One, 'debt' for Oracle Two)
            </label>
            <input
              className="w-full rounded-md border border-gray-600 bg-back-neutral-tertiary px-3 py-2 text-fore-neutral-primary focus:border-accent-primary focus:outline-none"
              type="text"
              value={customFormula}
              onChange={(e) => setCustomFormula(e.target.value)}
              placeholder="e.g., coll / debt"
            />
            {formulaError && (
              <p className="mt-1 text-sm text-red-500">{formulaError}</p>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-fore-neutral-secondary">
              Examples:
            </span>
            {formulaExamples.map((example) => (
              <button
                key={example.name}
                onClick={() => setCustomFormula(example.formula)}
                className="rounded bg-gray-700 px-2 py-1 text-xs text-fore-neutral-primary hover:bg-gray-600"
              >
                {example.name}: {example.formula}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppInput
              className="mb-[8px]"
              label="Collateral Price"
              value={oracleOnePrice}
              type="text"
              onChange={(e) =>
                validateAndCall(
                  0,
                  Infinity,
                  Number(e.target.value),
                  setOracleOnePrice
                )
              }
            />
          </div>
          <div className="flex flex-col">
            <AppInput
              className="mb-[8px]"
              label="Collateral Deviation Threshold"
              value={oracleOneDeviationThresholdBPS}
              onChange={(e) =>
                validateAndCall(
                  0,
                  10000,
                  Number(e.target.value),
                  setOracleOneDeviationThresholdBPS
                )
              }
              type="text"
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppInput
              className="mb-[8px]"
              label="Debt Price"
              value={oracleTwoPrice}
              onChange={(e) =>
                validateAndCall(
                  0,
                  Infinity,
                  Number(e.target.value),
                  setOracleTwoPrice
                )
              }
              type="text"
            />
          </div>
          <div className="flex flex-col">
            <AppInput
              className="mb-[8px]"
              label="Debt Deviation Threshold"
              value={oracleTwoDeviationThresholdBPS}
              onChange={(e) =>
                validateAndCall(
                  0,
                  10000,
                  Number(e.target.value),
                  setOracleTwoDeviationThresholdBPS
                )
              }
              type="text"
            />
          </div>
        </div>

        <div className="mt-[20px] text-[15px] leading-[18px] text-fore-neutral-primary">
          {formulaError ? (
            <div className="rounded-md bg-red-900/20 border border-red-500 p-4">
              <p className="text-red-400 font-semibold">Formula Error</p>
              <p className="text-red-300 mt-1">{formulaError}</p>
              <p className="text-red-300 mt-2 text-sm">
                Please correct the formula to see results.
              </p>
            </div>
          ) : (
            <>
              <p>Face Value: {result.spot}</p>
              <p>Max Possible Value: {result.max}</p>
              <p>Min Possible Value: {result.min}</p>
              <p>Max Delta: {(result.max / result.min) * 100}</p>
              <p>Max Up: {(result.max / result.spot) * 100}</p>
              <p>Max Down: {(result.min / result.spot) * 100}</p>
            </>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
}
