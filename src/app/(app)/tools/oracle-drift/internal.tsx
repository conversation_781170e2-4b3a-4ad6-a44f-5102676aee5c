"use client";
import { useState, useMemo } from "react";
import { AppPageTitle } from "@/app/components/app-page-title";
import Footer from "@/app/components/Footer/Footer";
import { doubleDrift } from "./lib";
import { AppInput } from "@/app/components/app-input";
import {
  evaluateFormula,
  defaultFormula,
  formulaExamples,
} from "./formula-evaluator";
import { Body2 } from "@/app/components/app-typography";
import { AppHeader } from "@/app/(app)/components/app-header";
import { AppButton } from "@/app/components/app-button";

export default function OracleDriftPageInternal() {
  const [oracleOnePrice, setOracleOnePrice] = useState<any>("358189942348");
  const [oracleOneDeviationThresholdBPS, setOracleOneDeviationThresholdBPS] =
    useState<any>("25");
  const [oracleTwoPrice, setOracleTwoPrice] = useState<any>("99989574");
  const [oracleTwoDeviationThresholdBPS, setOracleTwoDeviationThresholdBPS] =
    useState<any>("50");
  const [customFormula, setCustomFormula] = useState<string>(defaultFormula);
  const [formulaError, setFormulaError] = useState<string>("");

  const validateAndCall = (
    min: number,
    max: number,
    value: number,
    fn: (input: any) => any
  ) => {
    if (value < min || value > max) {
      return false;
    }
    return fn(value);
  };

  const formulaFn = useMemo(() => {
    try {
      setFormulaError("");
      const testResult = evaluateFormula(customFormula, { coll: 1, debt: 1 });
      return (coll: number, debt: number) =>
        evaluateFormula(customFormula, { coll, debt });
    } catch (error) {
      setFormulaError(error.message);
      return null;
    }
  }, [customFormula]);

  const result = useMemo(() => {
    if (!formulaFn) {
      return { spot: 0, max: 0, min: 0 };
    }
    try {
      return doubleDrift(
        Number(oracleOnePrice),
        Number(oracleOneDeviationThresholdBPS),
        Number(oracleTwoPrice),
        Number(oracleTwoDeviationThresholdBPS),
        formulaFn
      );
    } catch (error) {
      setFormulaError(`Error calculating result: ${error.message}`);
      return { spot: 0, max: 0, min: 0 };
    }
  }, [
    oracleOnePrice,
    oracleOneDeviationThresholdBPS,
    oracleTwoPrice,
    oracleTwoDeviationThresholdBPS,
    formulaFn,
  ]);

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <AppHeader skipUser />
      <div className="min-h-[800px] p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">Oracle Drift</AppPageTitle>

          <Body2 className="mb-[3px]">
            Given 2 Oracle prices and their Deviation Threshold
          </Body2>
          <Body2 className="mb-[3px]">
            The tool will calculate the max and min possible values for the
            oracle, and the max delta between the two.
          </Body2>
          <Body2 className="mb-[3px]">
            NOTE: You can alter the formula by typing in the textarea or
            clicking the buttons for presets
          </Body2>
        </div>

        <div className="mb-[20px]">
          <div className="mb-[10px]">
            <AppInput
              className="mb-[8px]"
              label="Custom Formula (use 'coll' for Oracle One, 'debt' for Oracle Two)"
              value={customFormula}
              onChange={(e) => setCustomFormula(e.target.value)}
              type="text"
              placeholder="e.g., coll / debt"
            />
            {formulaError && (
              <Body2 className="mt-1 text-status-error">{formulaError}</Body2>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            <Body2 className="text-fore-neutral-secondary">Examples:</Body2>
            {formulaExamples.map((example) => (
              <AppButton
                key={example.name}
                onClick={() => setCustomFormula(example.formula)}
                variant="secondary"
                size="xs"
              >
                {example.name}: {example.formula}
              </AppButton>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppInput
              className="mb-[8px]"
              label="Collateral Price"
              value={oracleOnePrice}
              type="text"
              onChange={(e) =>
                validateAndCall(
                  0,
                  Infinity,
                  Number(e.target.value),
                  setOracleOnePrice
                )
              }
            />
          </div>
          <div className="flex flex-col">
            <AppInput
              className="mb-[8px]"
              label="Collateral Deviation Threshold"
              value={oracleOneDeviationThresholdBPS}
              onChange={(e) =>
                validateAndCall(
                  0,
                  10000,
                  Number(e.target.value),
                  setOracleOneDeviationThresholdBPS
                )
              }
              type="text"
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppInput
              className="mb-[8px]"
              label="Debt Price"
              value={oracleTwoPrice}
              onChange={(e) =>
                validateAndCall(
                  0,
                  Infinity,
                  Number(e.target.value),
                  setOracleTwoPrice
                )
              }
              type="text"
            />
          </div>
          <div className="flex flex-col">
            <AppInput
              className="mb-[8px]"
              label="Debt Deviation Threshold"
              value={oracleTwoDeviationThresholdBPS}
              onChange={(e) =>
                validateAndCall(
                  0,
                  10000,
                  Number(e.target.value),
                  setOracleTwoDeviationThresholdBPS
                )
              }
              type="text"
            />
          </div>
        </div>

        <div className="mt-[20px]">
          {formulaError ? (
            <div className="rounded-md bg-status-error/10 border border-status-error p-4">
              <Body2 className="text-status-error font-semibold">
                Formula Error
              </Body2>
              <Body2 className="text-status-error mt-1">{formulaError}</Body2>
              <Body2 className="text-status-error mt-2">
                Please correct the formula to see results.
              </Body2>
            </div>
          ) : (
            <>
              <Body2>Face Value: {result.spot}</Body2>
              <Body2>Max Possible Value: {result.max}</Body2>
              <Body2>Min Possible Value: {result.min}</Body2>
              <Body2>Max Delta: {(result.max / result.min) * 100}</Body2>
              <Body2>Max Up: {(result.max / result.spot) * 100}</Body2>
              <Body2>Max Down: {(result.min / result.spot) * 100}</Body2>
            </>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
}
