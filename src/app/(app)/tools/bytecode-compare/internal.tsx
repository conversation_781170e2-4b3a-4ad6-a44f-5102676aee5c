"use client";
import { AppLogo } from "@/app/components/app-logo";
import { AppPageTitle } from "@/app/components/app-page-title";
import { AppTextarea } from "@/app/components/app-textarea";
import Footer from "@/app/components/Footer/Footer";
import type { BytecodeComparisonResult } from "@/lib/utils";
import { compareBytecode } from "@/lib/utils";
import Link from "next/link";
import { useState } from "react";
import { AppButton } from "../../../components/app-button";

export default function BytecodeCompareInternal() {
  const [bytecodeOne, setBytecodeOne] = useState("");
  const [bytecodeTwo, setBytecodeTwo] = useState("");
  const [ignoreHash, setIgnoreHash] = useState(true);
  const [ignoreCbor, setIgnoreCbor] = useState(true);
  const [comparisonResult, setComparisonResult] =
    useState<BytecodeComparisonResult | null>(null);
  const [isComparing, setIsComparing] = useState(false);

  const handleCompare = () => {
    if (!bytecodeOne || !bytecodeTwo) {
      alert("Please provide both bytecode inputs");
      return;
    }

    setIsComparing(true);
    try {
      const result = compareBytecode(
        bytecodeOne,
        bytecodeTwo,
        ignoreHash,
        ignoreCbor
      );
      setComparisonResult(result);
    } catch (error) {
      console.error("Error during bytecode comparison:", error);
      alert("An error occurred during comparison");
    } finally {
      setIsComparing(false);
    }
  };

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="gradient-dark-bg flex items-center justify-between bg-back-neutral-tertiary px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      <div className="min-h-[800px] p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">Bytecode Compare</AppPageTitle>

          <p className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
            This tool compares two bytecode strings to identify differences
          </p>
          <p className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
            Options for ignoring bytecode hash and CBOR metadata help identify
            functional differences
          </p>
        </div>
        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppTextarea
              className="mb-[8px]"
              label="Bytecode 1"
              value={bytecodeOne}
              onChange={(e) => setBytecodeOne(e.target.value)}
            />
            {bytecodeOne.length > 0 && (
              <span className="mb-2 text-[16px] text-white">
                Bytecode length: {bytecodeOne.length}
              </span>
            )}
          </div>
          <div className="flex flex-col">
            <AppTextarea
              className="mb-[8px]"
              label="Bytecode 2"
              value={bytecodeTwo}
              onChange={(e) => setBytecodeTwo(e.target.value)}
            />
            {bytecodeTwo.length > 0 && (
              <span className="mb-2 text-[16px] text-white">
                Bytecode length: {bytecodeTwo.length}
              </span>
            )}
          </div>
        </div>

        <div className="mb-4 flex items-center gap-4">
          <label className="flex items-center gap-2 text-fore-neutral-secondary">
            <input
              type="checkbox"
              checked={ignoreHash}
              onChange={(e) => setIgnoreHash(e.target.checked)}
              className="size-4"
            />
            Ignore hash
          </label>

          <label className="flex items-center gap-2 text-fore-neutral-secondary">
            <input
              type="checkbox"
              checked={ignoreCbor}
              onChange={(e) => setIgnoreCbor(e.target.checked)}
              className="size-4"
            />
            Ignore cbor
          </label>
        </div>

        <AppButton onClick={handleCompare} disabled={isComparing}>
          {isComparing ? "Comparing..." : "Compare"}
        </AppButton>

        {comparisonResult && (
          <div className="mt-6 rounded-md bg-back-neutral-tertiary p-4 text-fore-neutral-primary">
            <h2 className="mb-4 text-xl font-semibold">Comparison Results</h2>

            <div className="mb-6">
              {comparisonResult.isIdentical ? (
                <div className="font-bold text-green-500">
                  ✅ The bytecodes are functionally identical (ignoring
                  specified metadata sections).
                </div>
              ) : (
                <div className="font-bold text-red-500">
                  ❌ The bytecodes are different, even ignoring specified
                  metadata sections.
                </div>
              )}
            </div>

            {(comparisonResult.replacedSections.file1.length > 0 ||
              comparisonResult.replacedSections.file2.length > 0) && (
              <div className="mb-6">
                <h3 className="mb-2 text-lg font-medium">
                  Replaced sections for comparison:
                </h3>

                {comparisonResult.replacedSections.file1.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium">
                      File 1 ({comparisonResult.replacedSections.file1.length}{" "}
                      sections):
                    </h4>
                    <ul className="list-inside list-disc pl-4">
                      {comparisonResult.replacedSections.file1.map(
                        (section, i) => (
                          <li key={i} className="mb-1">
                            {section.type} at position {section.position}-
                            {section.position + section.length - 1}
                            <div className="text-xs text-fore-neutral-secondary">
                              Content: {section.content.substring(0, 30)}
                              {section.content.length > 30 ? "..." : ""}
                            </div>
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}

                {comparisonResult.replacedSections.file2.length > 0 && (
                  <div>
                    <h4 className="font-medium">
                      File 2 ({comparisonResult.replacedSections.file2.length}{" "}
                      sections):
                    </h4>
                    <ul className="list-inside list-disc pl-4">
                      {comparisonResult.replacedSections.file2.map(
                        (section, i) => (
                          <li key={i} className="mb-1">
                            {section.type} at position {section.position}-
                            {section.position + section.length - 1}
                            <div className="text-xs text-fore-neutral-secondary">
                              Content: {section.content.substring(0, 30)}
                              {section.content.length > 30 ? "..." : ""}
                            </div>
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {!comparisonResult.isIdentical &&
              comparisonResult.differences.length > 0 && (
                <div className="mb-6">
                  <h3 className="mb-2 text-lg font-medium">
                    Found {comparisonResult.differences.length} difference
                    {comparisonResult.differences.length !== 1 ? "s" : ""}:
                  </h3>

                  {comparisonResult.differences.map((diff, index) => (
                    <div key={index} className="mb-4 rounded bg-gray-800 p-3">
                      <h4 className="font-medium">Difference #{index + 1}:</h4>
                      <div>
                        Position: {diff.start} to {diff.end} (length:{" "}
                        {diff.end - diff.start + 1})
                      </div>
                      <div className="mt-2">
                        <div>File 1: {diff.content1 || "(missing)"}</div>
                        <div>File 2: {diff.content2 || "(missing)"}</div>
                      </div>

                      <div className="mt-2">
                        <h5 className="font-medium">Context:</h5>
                        <div className="overflow-x-auto">
                          <pre className="text-xs">
                            File 1:{" "}
                            {diff.context1.substring(
                              0,
                              diff.start -
                                (diff.start - 16 > 0 ? diff.start - 16 : 0)
                            )}
                            <span className="bg-red-900 px-1">
                              {diff.content1}
                            </span>
                            {diff.context1.substring(diff.content1.length)}
                          </pre>
                          <pre className="text-xs">
                            File 2:{" "}
                            {diff.context2.substring(
                              0,
                              diff.start -
                                (diff.start - 16 > 0 ? diff.start - 16 : 0)
                            )}
                            <span className="bg-green-900 px-1">
                              {diff.content2}
                            </span>
                            {diff.context2.substring(diff.content2.length)}
                          </pre>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

            {!comparisonResult.isIdentical && (
              <div>
                <h3 className="text-lg font-medium">Summary:</h3>
                <div>
                  {comparisonResult.summary.totalDiffLength} different
                  characters ({comparisonResult.summary.percentDiff}% of the
                  analyzed bytecode)
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      <Footer />
    </div>
  );
}
