"use client";
import { useEffect, useState } from "react";
import { AppPageTitle } from "@/app/components/app-page-title";
import Footer from "@/app/components/Footer/Footer";
import { AppTextarea } from "@/app/components/app-textarea";
import { formatHexData } from "@/lib/utils";
import { AppCode } from "@/app/components/app-code";
import { AppButton } from "@/app/components/app-button";
import { Body2 } from "@/app/components/app-typography";
import { AppHeader } from "@/app/(app)/components/app-header";

export default function BytecodeFormatterInternal() {
  const [bytecode, setBytecode] = useState("");
  const [mode, setMode] = useState<"words" | "calldata">("calldata");

  const [comparisonResult, setComparisonResult] = useState<string | null>(null);

  useEffect(() => {
    setComparisonResult(formatHexData(bytecode, mode));
  }, [bytecode, mode]);

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <AppHeader skipUser />
      <div className="min-h-[800px] p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">Bytecode Formatter</AppPageTitle>

          <AppButton
            onClick={() => {
              if (mode == "words") {
                setMode("calldata");
              } else {
                setMode("words");
              }
            }}
          >
            As {mode}
          </AppButton>

          <Body2 className="mb-[3px]">
            This tool formats bytecode and calldata into 32 bytes per row
          </Body2>
          <Body2 className="mb-[3px]">
            Calldata has it's selectors (first 4 bytes) separate as well
          </Body2>
        </div>
        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppTextarea
              className="mb-[8px]"
              label={mode}
              value={bytecode}
              onChange={(e) => setBytecode(e.target.value)}
            />
          </div>
        </div>

        <AppCode
          showLineNumbers={false}
          code={comparisonResult}
          language="python"
        />
      </div>
      <Footer />
    </div>
  );
}
