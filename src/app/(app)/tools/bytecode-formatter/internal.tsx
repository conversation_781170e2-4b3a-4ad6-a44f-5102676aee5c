"use client";
import Link from "next/link";
import { useEffect, useState } from "react";
import { AppLogo } from "@/app/components/app-logo";
import { AppPageTitle } from "@/app/components/app-page-title";
import Footer from "@/app/components/Footer/Footer";
import { AppTextarea } from "@/app/components/app-textarea";
import { formatHexData } from "@/lib/utils";
import { AppCode } from "@/app/components/app-code";
import { AppButton } from "@/app/components/app-button";

export default function BytecodeFormatterInternal() {
  const [bytecode, setBytecode] = useState("");
  const [mode, setMode] = useState<"words" | "calldata">("calldata");

  const [comparisonResult, setComparisonResult] = useState<string | null>(null);

  useEffect(() => {
    setComparisonResult(formatHexData(bytecode, mode));
  }, [bytecode, mode]);

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="gradient-dark-bg flex items-center justify-between bg-back-neutral-tertiary px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      <div className="min-h-[800px] p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">Bytecode Formatter</AppPageTitle>

          <AppButton
            onClick={() => {
              if (mode == "words") {
                setMode("calldata");
              } else {
                setMode("words");
              }
            }}
          >
            As {mode}
          </AppButton>

          <p className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
            This tool formats bytecode and calldata into 32 bytes per row
          </p>
          <p className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
            Calldata has it's selectors (first 4 bytes) separate as well
          </p>
        </div>
        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppTextarea
              className="mb-[8px]"
              label={mode}
              value={bytecode}
              onChange={(e) => setBytecode(e.target.value)}
            />
          </div>
        </div>

        <AppCode
          showLineNumbers={false}
          code={comparisonResult}
          language="python"
        />
      </div>
      <Footer />
    </div>
  );
}
