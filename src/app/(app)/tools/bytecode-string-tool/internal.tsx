"use client";
import { AppLogo } from "@/app/components/app-logo";
import { AppPageTitle } from "@/app/components/app-page-title";
import { AppTextarea } from "@/app/components/app-textarea";
import Footer from "@/app/components/Footer/Footer";
import Link from "next/link";
import { useMemo, useState } from "react";

import { AppCode } from "@/app/components/app-code";

function stringToHex(input: string, padToBytes: number = 0): string {
  // Convert string to UTF-8 bytes
  const encoder = new TextEncoder();
  const bytes = encoder.encode(input);

  // Convert bytes to hex
  const hex = Array.from(bytes)
    .map((byte) => byte.toString(16).padStart(2, "0"))
    .join("");

  // Add length prefix (number of bytes)
  const lengthHex = bytes.length.toString(16).padStart(2, "0");
  const result = `0x${lengthHex}${hex}`;

  // Pad if requested
  if (padToBytes > 0) {
    const currentLength = result.length - 2; // Subtract '0x'
    const paddingNeeded = padToBytes * 2 - currentLength;
    if (paddingNeeded > 0) {
      return result + "0".repeat(paddingNeeded);
    }
  }

  return result;
}

export default function BytecodeStringToolInternal() {
  const [string, setString] = useState("");

  const encoded = useMemo(() => {
    return stringToHex(string);
  }, [string]);

  const padded = useMemo(() => {
    return stringToHex(string, 32);
  }, [string]);

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="gradient-dark-bg flex items-center justify-between bg-back-neutral-tertiary px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      <div className="min-h-[800px] p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">Bytecode String Tool</AppPageTitle>

          <p className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
            This tool encodes strings into UTF-8 Bytes
          </p>
          <p className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
            It also adds a length value as a prefix and pads the length to 32
            bytes
          </p>
        </div>
        <div className="grid grid-cols-2 gap-10">
          <div className="flex flex-col">
            <AppTextarea
              className="mb-[8px]"
              label={"String (Max 32 chars)"}
              value={string}
              onChange={(e) => setString(e.target.value)}
            />
          </div>
        </div>

        <h3 className="text-fore-neutral-primary">Encoded</h3>
        <AppCode showLineNumbers={false} code={encoded} language="python" />
        <h3 className="text-fore-neutral-primary">Encoded and Padded</h3>
        <AppCode showLineNumbers={false} code={padded} language="python" />
      </div>
      <Footer />
    </div>
  );
}
