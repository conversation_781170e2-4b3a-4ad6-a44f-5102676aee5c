import { useEffect, useMemo, useRef, useState } from "react";
import { FiChevronDown, FiChevronUp, FiSearch } from "react-icons/fi";

import { usePersistedTheme } from "@/app/services/useThemePersistence";
import { cn } from "../../helpers/cn";
import { THEME_OPTIONS } from "../../services/ThemeProvider";

interface Item {
  label: string;
  value: string | number;
}

interface SearchDropdownProps {
  items: Item[];
  onChange: (value: string | number) => void;
  searchPlaceholder: string;
  className?: string;
  disabled?: boolean;
  error?: string;
  label?: string;
}

//TODO - add keyboard support

export const SearchDropdown = ({
  items,
  onChange,
  searchPlaceholder,
  className = "",
  disabled = false,
  error,
  label,
}: SearchDropdownProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [search, setSearch] = useState("");
  const { theme: currentTheme } = usePersistedTheme();
  const isDark = currentTheme === THEME_OPTIONS.dark;

  useEffect(() => {
    if (!isOpen) return;
    const onClickOutside = (e: MouseEvent) => {
      if (ref.current && !ref.current.contains(e.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("click", onClickOutside, true);

    return () => {
      document.removeEventListener("click", onClickOutside, true);
    };
  }, [isOpen]);

  const toggleOpen = () => {
    if (disabled) return;
    setIsOpen((prev) => !prev);
    if (isOpen) setSearch("");
  };

  const selectItem = (item: Item) => {
    setSelectedItem(item);
    onChange(item.value);
    setIsOpen(false);
    setSearch("");
  };

  const filteredItems = useMemo(() => {
    if (!search) return items;
    return items.filter((item) => new RegExp(search, "i").test(item.label));
  }, [search, items]);

  const getButtonStyles = () => {
    const baseStyles =
      "h-[40px] rounded-[6px] px-4 w-full min-w-[200px] outline-none transition-all duration-200 font-bold text-[16px] leading-[1.375] flex items-center justify-between";

    if (disabled) {
      return cn(
        baseStyles,
        isDark
          ? "bg-transparent border border-white/60 text-white/60 opacity-40"
          : "bg-transparent border border-black/60 text-black/60 opacity-40"
      );
    }

    if (error) {
      return cn(
        baseStyles,
        isDark
          ? "bg-transparent border border-red-500 text-white/60 hover:border-red-400 focus:border-red-500"
          : "bg-transparent border border-red-500 text-black/60 hover:border-red-400 focus:border-red-500"
      );
    }

    return cn(
      baseStyles,
      isDark
        ? "bg-transparent border border-white/60 text-white/60 hover:border-white focus:border-white/60"
        : "bg-transparent border border-black/60 text-black/60 hover:border-black focus:border-black/60",
      isOpen && "border-accent-primary"
    );
  };

  const getSearchInputStyles = () => {
    return cn(
      "h-[40px] rounded-[6px] px-4 pl-10 w-full outline-none transition-all duration-200 font-bold text-[16px] leading-[1.375]",
      isDark
        ? "bg-transparent border border-white/60 text-white/60 placeholder:text-white/60"
        : "bg-transparent border border-black/60 text-black/60 placeholder:text-black/60"
    );
  };

  return (
    <div className={cn("relative w-full", className)}>
      {label && (
        <label
          className={cn(
            "mb-[3px] block text-[15px] leading-[18px]",
            isDark ? "text-white/80" : "text-black/80"
          )}
        >
          {label}
        </label>
      )}

      <div ref={ref} className="relative">
        <button
          type="button"
          className={getButtonStyles()}
          onClick={toggleOpen}
          disabled={disabled}
        >
          <span className="truncate text-left">
            {selectedItem?.label || searchPlaceholder}
          </span>
          {isOpen ? (
            <FiChevronUp className="size-4 shrink-0" />
          ) : (
            <FiChevronDown className="size-4 shrink-0" />
          )}
        </button>

        {isOpen && (
          <div
            className={cn(
              "absolute top-full left-0 right-0 z-50 mt-1 rounded-[6px] border shadow-lg",
              isDark
                ? "bg-gray-800 border-white/60"
                : "bg-white border-black/60"
            )}
          >
            {/* Search Input */}
            <div className="relative p-2">
              <FiSearch
                className={cn(
                  "absolute left-5 top-1/2 -translate-y-1/2 size-4",
                  isDark ? "text-white/60" : "text-black/60"
                )}
              />
              <input
                type="text"
                className={getSearchInputStyles()}
                placeholder={searchPlaceholder}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                autoFocus
              />
            </div>

            {/* Items List */}
            <div className="max-h-48 overflow-y-auto">
              {filteredItems.length > 0 ? (
                filteredItems.map((item) => (
                  <button
                    key={item.value}
                    type="button"
                    className={cn(
                      "w-full px-4 py-2 text-left transition-colors duration-200 text-[15px] font-medium",
                      isDark
                        ? "text-white/80 hover:bg-white/10"
                        : "text-black/80 hover:bg-black/5",
                      selectedItem?.value === item.value &&
                        "bg-accent-primary/10"
                    )}
                    onClick={() => selectItem(item)}
                  >
                    {item.label}
                  </button>
                ))
              ) : (
                <div
                  className={cn(
                    "px-4 py-2 text-[15px] font-medium",
                    isDark ? "text-white/60" : "text-black/60"
                  )}
                >
                  No items found
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {error && (
        <span className="mt-[3px] block h-[14px] text-[12px] text-red-500">
          {error}
        </span>
      )}
    </div>
  );
};
