import type { Fuzzer, FuzzingResults } from "@recon-fuzz/log-parser";
import {
  echidnaLogsToFunctions,
  halmosSequenceToFunction,
  medusaLogsToFunctions,
  processLogs,
} from "@recon-fuzz/log-parser";
import { useEffect, useState } from "react";
import { IoMdDownload } from "react-icons/io";

import { ENV_TYPE } from "@/app/app.constants";
import type {
  BrokenPropShow,
  TracesShower,
  VmParsingData,
} from "@/app/types/types";

import { AppButton } from "../app-button";
import { AppCode } from "../app-code";
import styles from "./LogComponent.module.scss";

interface LogComponentProps {
  fuzzer: ENV_TYPE;
  logs: string;
  prefix?: string;
  jobStatsForced?: FuzzingResults;
}

export default function LogComponent({
  fuzzer,
  logs,
  prefix,
  jobStatsForced,
}: LogComponentProps) {
  const [showTrace, setShowTrace] = useState<TracesShower[]>(null);
  const [useVmData, setUseVmData] = useState<VmParsingData[]>();
  const [traces, setTraces] = useState<string[]>([]);
  const [jobStatsGlobal, setJobStatsGlobal] = useState<FuzzingResults>();
  const [showBrokenProp, setShowBrokenProp] = useState<BrokenPropShow[]>(null);
  const [isCopied, setIsCopied] = useState(false);

  useEffect(() => {
    let jobStats: FuzzingResults = {
      duration: "0",
      coverage: 0,
      failed: 0,
      passed: 0,
      numberOfTests: 0,
      results: [],
      traces: [],
      brokenProperties: [],
    };
    if (jobStatsForced && jobStatsForced.brokenProperties.length > 0) {
      jobStats = jobStatsForced;
      setJobStatsGlobal(jobStats);
    } else if (logs && fuzzer) {
      jobStats = processLogs(logs, fuzzer as unknown as Fuzzer); /// TODO 0xsi please fix -Alex
      setJobStatsGlobal(jobStats);
    }
    if (logs && fuzzer && jobStats.brokenProperties.length > 0) {
      setTraces(jobStats.brokenProperties.map((el) => el.sequence));
      const initialState = jobStats.brokenProperties.map(() => ({
        roll: true,
        time: true,
        prank: true,
      }));
      setUseVmData(initialState);
      jobStats.brokenProperties.forEach((trace, index) => {
        setShowTrace((prev) => [
          ...(prev || []),
          {
            id: index,
            show: false,
          },
        ]);
        setShowBrokenProp((prev) => [
          ...(prev || []),
          {
            id: index,
            show: false,
          },
        ]);
      });
    }
  }, [logs, fuzzer, jobStatsForced]);

  const showTracesHandler = (index) => {
    setShowTrace((prev) =>
      prev.map((trace) => {
        if (trace.id === index) {
          return {
            ...trace,
            show: !trace.show,
          };
        }
        return trace;
      })
    );
  };

  const showBrokenPropHandler = (index) => {
    setShowBrokenProp((prev) =>
      prev.map((trace) => {
        if (trace.id === index) {
          return {
            ...trace,
            show: !trace.show,
          };
        }
        return trace;
      })
    );
  };

  const handleVmData = (index: number, property: string) => {
    setUseVmData((prevState) =>
      prevState.map((item, i) =>
        i === index
          ? {
              ...item,
              [property]: !item[property],
            }
          : item
      )
    );
  };

  const isActiveVmData = (index: number, property: string) =>
    useVmData[index][property];

  const prepareTrace = (
    trace: string,
    index: number,
    brokenProperties: string
  ) => {
    let finalTrace = "";
    if (fuzzer === ENV_TYPE.MEDUSA) {
      finalTrace = medusaLogsToFunctions(
        trace,
        prefix ?? index.toString(),
        useVmData[index]
      );
    } else if (fuzzer === ENV_TYPE.ECHIDNA) {
      finalTrace = echidnaLogsToFunctions(
        trace,
        prefix ?? index.toString(),
        brokenProperties,
        useVmData[index]
      );
    } else if (fuzzer === ENV_TYPE.HALMOS) {
      finalTrace = halmosSequenceToFunction(
        trace,
        brokenProperties,
        prefix ?? index.toString(),
        index
      );
    }
    const functionName = finalTrace
      .split("() public")[0]
      .replace("function ", "");
    const forgeCommand =
      `// forge test --match-test ${functionName} -vvv`.replace("\n", "");
    return `${forgeCommand} \n${finalTrace}`;
  };

  const getBrokenPropName = (index: number) => {
    const brkProp = jobStatsGlobal.brokenProperties.find(
      (_, id) => index === id
    )?.brokenProperty;
    return brkProp;
  };

  const copyAllHandler = async () => {
    try {
      const allFn = traces.map((trace, index) => {
        return prepareTrace(
          trace,
          index,
          jobStatsGlobal.brokenProperties.find((el) => el.sequence === trace)
            ?.brokenProperty
        );
      });

      await navigator.clipboard.writeText(allFn.join("\n\n"));
      setIsCopied(true);
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  };

  return (
    <div>
      {traces.length > 0 ? (
        <div className="flex flex-row items-center justify-between">
          <h3 className="my-[20px] text-[25px] leading-[28px] text-fore-neutral-primary">
            {traces.length} Broken propert{traces.length === 1 ? "y" : "ies"}
          </h3>
          <AppButton
            className="bg-accent-primary px-[40px] py-[10px] text-[18px] leading-[21px]"
            onClick={copyAllHandler}
            rightIcon={isCopied ? null : <IoMdDownload />}
          >
            Copy all repro {isCopied ? "(Copied ✅)" : ""}
          </AppButton>
        </div>
      ) : (
        ""
      )}
      {traces.length > 0
        ? traces.map((trace, index) => (
            <div key={index}>
              <h2
                onClick={() => showBrokenPropHandler(index)}
                className="cursor-pointer p-3 text-xl text-white"
              >
                {`${index + 1} - ${getBrokenPropName(index)} ${
                  showBrokenProp.find((el) => el.id === index).show ? "▲" : "▼"
                }`}
              </h2>
              {showBrokenProp.find((el) => el.id === index).show ? (
                <div>
                  <div className={styles.main}>
                    <div className={styles.vm_buttons}>
                      <AppButton
                        className={`w-[135px] ${
                          isActiveVmData(index, "prank")
                            ? "bg-accent-primary"
                            : "bg-[#171717]"
                        } px-[14px] py-[9px] leading-[21px]`}
                        onClick={() => handleVmData(index, "prank")}
                      >
                        <span>Use vm.prank</span>
                      </AppButton>
                      <AppButton
                        className={`w-[135px] ${
                          isActiveVmData(index, "roll")
                            ? "bg-accent-primary"
                            : "bg-[#171717]"
                        } px-[14px] py-[9px] leading-[21px]`}
                        onClick={() => handleVmData(index, "roll")}
                      >
                        <span>Use vm.roll</span>
                      </AppButton>
                      <AppButton
                        className={`w-[135px] ${
                          isActiveVmData(index, "time")
                            ? "bg-accent-primary"
                            : "bg-[#171717]"
                        } px-[14px] py-[9px] leading-[21px]`}
                        onClick={() => handleVmData(index, "time")}
                      >
                        <span>Use vm.warp</span>
                      </AppButton>
                    </div>
                    <div className={styles.traces_button}>
                      <AppButton
                        className={
                          "mb-3 w-[120px] bg-[#171717] px-[14px] py-[9px] leading-[21px]"
                        }
                        onClick={() => showTracesHandler(index)}
                      >
                        <span>
                          {showTrace.find((el) => el.id === index).show
                            ? "Hide trace"
                            : "Show trace"}
                        </span>
                      </AppButton>
                    </div>
                    <AppCode
                      key={index}
                      code={prepareTrace(
                        trace,
                        index,
                        jobStatsGlobal.brokenProperties.find(
                          (el) => el.sequence === trace
                        ).brokenProperty
                      )}
                      language="javascript"
                    />
                  </div>
                  <div>
                    {showTrace.find((el) => el.id === index).show ? (
                      <AppCode code={traces[index]} language="bash" />
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              ) : (
                ""
              )}
            </div>
          ))
        : ""}
    </div>
  );
}
