"use client";

import { H1, Body3 } from "../app-typography";

interface PageHeaderProps {
  title: string;
  descriptions: string[];
  className?: string;
}

export function PageHeader({
  title,
  descriptions,
  className = "",
}: PageHeaderProps) {
  return (
    <div className={`flex flex-col gap-1 self-stretch ${className}`}>
      <H1 color="accent-alt" as="h1" className="text-accent-primary">
        {title}
      </H1>

      <div className="flex flex-col gap-2">
        {descriptions.map((description) => (
          <Body3 key={description} color="secondary" as="p">
            {description}
          </Body3>
        ))}
      </div>
    </div>
  );
}
