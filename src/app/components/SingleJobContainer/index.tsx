"use client";
import { generateJobMD, processLogs } from "@recon-fuzz/log-parser";
import type { Fuzz<PERSON>, FuzzingResults } from "@recon-fuzz/log-parser";
import axios from "axios";
import Link from "next/link";
import { useEffect, useState } from "react";
import { FaCheckCircle } from "react-icons/fa";
import { IoMdDownload } from "react-icons/io";
import { ImLoop2 } from "react-icons/im";

import { AppButton } from "@/app/components/app-button";
import { AppLogo } from "@/app/components/app-logo";
import { AppSpinner } from "@/app/components/app-spinner";
import { downloadFile } from "@/app/(app)/tools/mdReportHelper";

import LogComponent from "../LogComponent/LogComponent";
import { ShareJobButton } from "./share-job-button";
import styles from "./SingleJob.module.scss";
import JobReport from "../JobReport/JobReport";

interface BrokenProp {
  traces: string;
  brokenProperty: string;
}

export default function SingleJobContainer({
  isJobInfoLoading,
  shareInfo,
  jobId,
  reloadShares,
  jobData,
  isLoading,
}) {
  const [logs, setLogs] = useState<string>("");
  const [showLogs, setShowLogs] = useState(true);
  const [showReport, setShowReport] = useState(true);
  const [jobStats, setJobStats] = useState<FuzzingResults | null>(null);
  const [terminate, setTerminate] = useState(false);
  const [brokenProperties, setBrokenProperties] = useState<BrokenProp[]>([]);
  const [showJobReport, setShowJobReport] = useState<boolean>(false);
  const [rawlogs, setRawLogs] = useState<string>("");

  const stopJob = async () => {
    setTerminate(!terminate);

    try {
      const stoppedJob = await axios({
        method: "POST",
        url: `/api/jobs/stop`,
        data: {
          jobId,
        },
      });
    } catch (e) {
      alert(`Something went wrong: ${e.response.data.message}`);
      setTerminate(!terminate);
    }
  };

  const createMarkdown = () => {
    const md = generateJobMD(
      jobData?.fuzzer as Fuzzer,
      rawlogs,
      jobData?.label || `${jobData?.orgName}/${jobData?.repoName}`
    );
    return md;
  };

  useEffect(() => {
    const fetchLogs = async () => {
      const jobLogsRaw = await axios({
        method: "POST",
        url: `/api/fetchLogs`,
        data: {
          logsUrl: jobData?.logsUrl,
        },
      });
      if (jobLogsRaw.status === 200) {
        const data = processLogs(jobLogsRaw.data, jobData?.fuzzer);
        setRawLogs(jobLogsRaw.data);
        return {
          jobStats: data,
          logs: jobLogsRaw.data,
        };
      } else {
        return null;
      }
    };

    // This a general function to fetch the broken props ( for new runs from end sept 2024 )
    // It will also fetch the data from the logs
    // As a transition, if the broken props are not found, we will parse the logs as usual
    // Otherwise, we will reconciliate both data
    const fetchData = async () => {
      const brokenProps = jobData?.brokenProperties || [];
      const jobInfo = {
        duration: jobData?.testsDuration,
        coverage: jobData?.testsCoverage,
        failed: jobData?.testsFailed,
        passed: jobData?.testsPassed,
        numberOfTests: jobData?.numberOfTests,
        results: [],
        traces: [],
        brokenProperties: brokenProps,
      };
      const { logs, jobStats } = await fetchLogs();

      if (brokenProps.length > 0) {
        setBrokenProperties(brokenProps);
        setLogs(brokenProps.map((prop) => prop.traces).join("\n\n"));
        setJobStats({
          duration: jobInfo.duration,
          coverage: jobInfo.coverage,
          failed: jobInfo.failed,
          passed: jobInfo.passed,
          results: jobStats.results,
          numberOfTests: jobInfo.numberOfTests,
          traces: brokenProps.map((el) => el.traces),
          brokenProperties: brokenProps.map((el) => {
            return {
              brokenProperty: el.brokenProperty,
              sequence: el.traces,
            };
          }),
        });
      } else {
        setLogs(logs);
        setJobStats(jobStats);
      }
    };

    if (jobData?.fuzzer === "ECHIDNA" || jobData?.fuzzer === "MEDUSA") {
      fetchData();
    } else if (jobData?.logsUrl && jobData?.fuzzer) {
      fetchLogs().then((data) => {
        if (data) {
          setLogs(data.logs);
          setJobStats(data.jobStats);
        }
      });
    }
  }, [
    jobData?.brokenProperties,
    jobData?.fuzzer,
    jobData?.logsUrl,
    jobData?.testsCoverage,
    jobData?.testsDuration,
    jobData?.testsFailed,
    jobData?.testsPassed,
    jobData?.numberOfTests,
    jobId,
  ]);

  const rerunHandler = async () => {
    try {
      const res = await axios({
        method: "POST",
        url: `/api/jobs/rerun`,
        data: {
          jobId,
        },
      });
      if (res.status === 200) {
        alert("New job started");
        setTimeout(() => {
          window.location.href = "/dashboard/jobs";
        }, 1000);
      }
    } catch (err) {
      alert(`Something went wrong: ${err.response.data.message}`);
    }
  };

  const isSharePage = window.location.href.includes("shares");

  return (
    <>
      <div className="gradient-dark-bg flex items-center justify-between bg-back-neutral-tertiary px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
        <div className="flex gap-[30px]">
          {!isJobInfoLoading && (
            <ShareJobButton
              jobId={jobId}
              reloadShares={reloadShares}
              shareInfo={shareInfo}
            />
          )}
          {jobData?.corpusUrl && (
            <Link target="_blank" href={jobData.corpusUrl}>
              <AppButton
                className=" bg-[#171717] px-[40px] py-[10px] text-[18px] leading-[21px]"
                rightIcon={<IoMdDownload />}
              >
                Download Corpus
              </AppButton>
            </Link>
          )}
          {!isSharePage && (
            <AppButton
              className=" bg-[#171717] px-[40px] py-[10px] text-[18px] leading-[21px]"
              onClick={rerunHandler}
              rightIcon={<ImLoop2 />}
            >
              Rerun Job
            </AppButton>
          )}
          {jobData?.status === "RUNNING" &&
            !terminate &&
            !window.location.href.includes("share") && (
              <AppButton
                className=" bg-[#E26453] px-[40px] py-[10px] text-[18px] leading-[21px]"
                onClick={stopJob}
              >
                <span>Terminate Job</span>
              </AppButton>
            )}
          {jobData?.status !== "STOPPED" &&
            terminate &&
            !window.location.href.includes("share") && (
              <p>
                <div className="mb-[10px] flex w-[340px] items-center justify-center gap-[13px] rounded-[8px] p-[14px] text-[18px] leading-[21px] text-fore-neutral-primary">
                  <FaCheckCircle className="size-[20px] text-fore-neutral-primary" />
                  Job will be terminated momentarily
                </div>
              </p>
            )}
        </div>
      </div>
      <div className={styles.spinner}>
        {isLoading && <AppSpinner size={50} />}
      </div>

      <div className="px-[45px] pt-[45px]">
        {/* {!isJobInfoLoading && shareInfo && (
          <p className="text-fore-neutral-primary my-[20px] text-[25px] leading-[28px]">
            You are sharing this via:{" "}
            <Link
              target="_blank"
              href={`/shares/${shareInfo.id}`}
              className="underline"
            >
              {shareInfo.id}
            </Link>
          </p>
        )} */}
        {jobData?.status === "QUEUED" ||
          jobData?.status === "RUNNING" ||
          (jobData?.status === "STARTED" && (
            <div className="w-full grow text-fore-neutral-primary">
              <h2 className="mb-[28px] text-[22px] leading-[26px]">
                Job status: {jobData?.status} ...
              </h2>
              <p>Please check back soon for results.</p>
            </div>
          ))}
        {!jobData && (
          <div className="w-full grow text-fore-neutral-primary">
            <h2 className="mb-[28px] text-[22px] leading-[26px]">
              job starting ...
            </h2>
          </div>
        )}

        {!isJobInfoLoading && jobData && jobData?.fuzzer && jobStats && (
          <div className={styles.fuzzerTitle}>
            <div>
              <h2
                className="mb-[40px] w-[240px] cursor-pointer text-[25px] leading-[28px] text-fore-neutral-primary"
                onClick={() => setShowJobReport(!showJobReport)}
              >
                Show Job Report {showJobReport ? "▲" : "▼"}
              </h2>
              {jobData?.fuzzer && jobStats && showJobReport ? (
                <JobReport
                  fuzzer={jobData.fuzzer}
                  jobStats={jobStats}
                  showBrokenProp={false}
                />
              ) : (
                ""
              )}
            </div>
            <div>
              {shareInfo ? (
                <Link href={`/shares/${shareInfo.id}/report`}>
                  <p className="rounded-md bg-accent-primary px-[14px] py-[9px] leading-[21px] text-white">
                    See report
                  </p>
                </Link>
              ) : (
                <AppButton
                  className="bg-accent-primary px-[14px] py-[9px] leading-[21px]"
                  onClick={() =>
                    downloadFile(createMarkdown(), jobData.repoName)
                  }
                >
                  <span>Download report</span>
                </AppButton>
              )}
            </div>
            {shareInfo && (
              <Link
                href={`https://github.com/${jobData.orgName}/${
                  jobData.repoName
                }${
                  jobData?.metadata?.commit
                    ? `/tree/${jobData.metadata.commit}`
                    : `/${jobData.ref}`
                }`}
                target="_blank"
                className="rounded-md bg-accent-primary px-[14px] py-[9px] leading-[21px] text-white"
              >
                Go to repo
              </Link>
            )}
          </div>
        )}

        <LogComponent
          fuzzer={jobData?.fuzzer}
          logs={logs}
          jobStatsForced={jobStats}
        />

        <div
          className={`mb-[28px] flex w-full flex-col ${
            (showLogs && !showReport) || (!showLogs && showReport)
              ? ""
              : "lg:flex lg:grid lg:grid-cols-2 lg:flex-col lg:gap-x-4"
          }`}
        >
          {logs && (
            <div className="w-full grow text-fore-neutral-primary">
              <div className="flex justify-between">
                <h2 className="mb-[28px] text-[22px] leading-[26px]">
                  {brokenProperties.length > 0 ? "Compressed logs" : "Logs"}
                </h2>
                <div className="mb-3 flex flex-row justify-between">
                  <AppButton
                    className={
                      "w-[180px] bg-[#171717] px-[14px] py-[9px] leading-[21px]"
                    }
                    onClick={() => setShowLogs(!showLogs)}
                  >
                    <span>{showLogs ? "Hide Logs" : "Show Logs"}</span>
                  </AppButton>
                </div>
                {jobData?.logsUrl && (
                  <Link
                    className="underline"
                    target="_blank"
                    href={jobData?.logsUrl}
                  >
                    Full logs
                  </Link>
                )}
              </div>

              {showLogs ? (
                <div className={styles.logContainer}>
                  <textarea name="logs" id="logs">
                    {logs}
                  </textarea>
                </div>
              ) : (
                ""
              )}
            </div>
          )}

          {jobData?.coverageUrl &&
          logs &&
          (jobData?.fuzzer === "ECHIDNA" || jobData?.fuzzer === "MEDUSA") ? (
            <div className="w-full grow text-fore-neutral-primary">
              <div className="flex justify-between">
                <h2 className="mb-[28px] text-[22px] leading-[26px]">
                  Coverage report
                </h2>
                <div className="mb-3 flex flex-row justify-between">
                  <AppButton
                    className={
                      "w-[180px] bg-[#171717] px-[14px] py-[9px] leading-[21px]"
                    }
                    onClick={() => setShowReport(!showReport)}
                  >
                    <span>
                      {showReport ? "Hide Coverage" : "Show Coverage"}
                    </span>
                  </AppButton>
                </div>
                {jobData?.coverageUrl && (
                  <Link
                    className="underline"
                    target="_blank"
                    href={jobData?.coverageUrl}
                  >
                    Open in a new tab
                  </Link>
                )}
              </div>

              {showReport ? (
                <iframe
                  src={jobData?.coverageUrl}
                  title="coverage"
                  className="h-[700px] w-full"
                />
              ) : (
                ""
              )}
            </div>
          ) : (
            ""
          )}
        </div>
      </div>
    </>
  );
}
